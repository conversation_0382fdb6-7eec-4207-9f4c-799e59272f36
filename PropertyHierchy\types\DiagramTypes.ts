// Property interface representing a property with hierarchical structure
export interface Property {
    id: string | number;
    name: string;
    type: string;
    parentPropertyId?: string | number;
    isMainProperty?: boolean;
    metadata?: Record<string, unknown>;
    children?: Property[];
}

// GoJS Node Data interface for property visualization
export interface PropertyNodeData {
    key: string | number;
    text: string;
    color?: string;
    size?: string;
    loc?: string;
    property: Property;
    isMainProperty?: boolean;
}

// GoJS Link Data interface for property relationships
export interface PropertyLinkData {
    from: string | number;
    to: string | number;
    text?: string;
    color?: string;
    relationship?: string;
}

// GoJS Model Data interface for property hierarchy
export interface PropertyModelData {
    nodeDataArray: PropertyNodeData[];
    linkDataArray?: PropertyLinkData[];
}

// Diagram Configuration interface
export interface DiagramConfig {
    layout?: string;
    nodeTemplate?: unknown;
    linkTemplate?: unknown;
    allowCopy?: boolean;
    allowDelete?: boolean;
    allowMove?: boolean;
    [key: string]: unknown;
}
