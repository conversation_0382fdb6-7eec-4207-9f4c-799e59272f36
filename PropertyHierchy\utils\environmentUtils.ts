/**
 * Environment detection utilities for PCF components
 * Determines whether the component is running in development (localhost) or production (CRM) environment
 */

/**
 * Detects if the component is running in development environment
 * @param context Optional PCF context for additional validation
 * @returns true if running in development mode, false if in production (CRM)
 */
export const isDevEnvironment = (context?: ComponentFramework.Context<unknown>): boolean => {
    // Check if window object is available (browser environment)
    if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        
        // Development environment indicators
        const isLocalhost = hostname === 'localhost' || 
                           hostname === '127.0.0.1' || 
                           hostname.includes('localhost');
        
        if (isLocalhost) {
            console.log('🔧 Environment Detection: Development mode detected (localhost)');
            return true;
        }
    }
    
    // Check if PCF context is available and valid
    if (!context) {
        console.log('🔧 Environment Detection: Development mode detected (no PCF context)');
        return true;
    }
    
    // Additional checks for CRM context validity
    try {
        // Try to access CRM-specific properties
        const hasWebAPI = context.webAPI && typeof context.webAPI.retrieveMultipleRecords === 'function';
        const hasMode = context.mode && typeof context.mode === 'object';
        
        if (!hasWebAPI || !hasMode) {
            console.log('🔧 Environment Detection: Development mode detected (invalid CRM context)');
            return true;
        }
        
        console.log('🚀 Environment Detection: Production mode detected (valid CRM context)');
        return false;
    } catch (error) {
        console.log('🔧 Environment Detection: Development mode detected (context access error):', error);
        return true;
    }
};

/**
 * Gets the current entity ID from PCF context
 * @param context PCF context
 * @returns Entity ID if available, null otherwise
 */
export const getCurrentEntityId = (context?: ComponentFramework.Context<unknown>): string | null => {
    if (!context) {
        console.log('📋 Entity ID Detection: No context available');
        return null;
    }
    
    try {
        // Try to get entity ID from context mode
        const entityId = (context.mode as { contextInfo?: { entityId?: string } })?.contextInfo?.entityId;
        
        if (entityId) {
            console.log('📋 Entity ID Detection: Found entity ID:', entityId);
            return entityId;
        }
        
        // Alternative method to get entity ID
        const alternativeId = (context as ComponentFramework.Context<unknown> & { page?: { entityId?: string } })?.page?.entityId;
        if (alternativeId) {
            console.log('📋 Entity ID Detection: Found alternative entity ID:', alternativeId);
            return alternativeId;
        }
        
        console.log('📋 Entity ID Detection: No entity ID found in context');
        return null;
    } catch (error) {
        console.log('📋 Entity ID Detection: Error accessing entity ID:', error);
        return null;
    }
};

/**
 * Gets environment information for debugging
 * @param context Optional PCF context
 * @returns Object with environment details
 */
export const getEnvironmentInfo = (context?: ComponentFramework.Context<unknown>) => {
    const info = {
        hostname: typeof window !== 'undefined' ? window.location.hostname : 'unknown',
        hasContext: !!context,
        hasWebAPI: !!(context?.webAPI),
        entityId: getCurrentEntityId(context),
        isDevMode: isDevEnvironment(context),
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
    };
    
    console.log('🔍 Environment Info:', info);
    return info;
};
