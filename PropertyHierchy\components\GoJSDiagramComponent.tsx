import React, { useEffect, useRef, useState } from 'react';
import * as go from 'gojs';
import { Property, PropertyNodeData, PropertyLinkData, PropertyModelData } from '../types/DiagramTypes';
import { PropertyDataService, DataLoadResult } from '../utils/propertyDataService';

// TreeView Component for displaying property hierarchy
interface TreeViewProps {
    properties: Property[];
    selectedPropertyId?: string | number;
    onPropertySelect?: (property: Property) => void;
}

const TreeView: React.FC<TreeViewProps> = ({ properties, selectedPropertyId, onPropertySelect }) => {
    // Initialize with all nodes expanded
    const [expandedNodes, setExpandedNodes] = useState<Set<string | number>>(() => {
        // Expand all properties that have children
        const propertiesWithChildren = properties.filter(p =>
            properties.some(child => child.parentPropertyId === p.id)
        );
        return new Set(propertiesWithChildren.map(p => p.id));
    });

    // Build tree structure from flat property array
    const buildTree = (properties: Property[]): Property[] => {
        const propertyMap = new Map<string | number, Property>();
        const rootProperties: Property[] = [];

        // First pass: create map of all properties
        properties.forEach(prop => {
            propertyMap.set(prop.id, { ...prop, children: [] });
        });

        // Second pass: build parent-child relationships
        properties.forEach(prop => {
            const property = propertyMap.get(prop.id)!;
            if (prop.parentPropertyId && propertyMap.has(prop.parentPropertyId)) {
                const parentProperty = propertyMap.get(prop.parentPropertyId)!;
                if (!parentProperty.children) parentProperty.children = [];
                parentProperty.children.push(property);
            } else {
                rootProperties.push(property);
            }
        });

        return rootProperties;
    };

    const toggleExpanded = (nodeId: string | number) => {
        const newExpanded = new Set(expandedNodes);
        if (newExpanded.has(nodeId)) {
            newExpanded.delete(nodeId);
        } else {
            newExpanded.add(nodeId);
        }
        setExpandedNodes(newExpanded);
    };

    const renderTreeNode = (property: Property & { children?: Property[] }, level = 0): React.ReactNode => {
        const hasChildren = property.children && property.children.length > 0;
        const isExpanded = expandedNodes.has(property.id);
        const isSelected = selectedPropertyId === property.id;

        return (
            <div key={property.id} className="tree-node">
                <div
                    className={`tree-node-content ${isSelected ? 'selected' : ''}`}
                    style={{ paddingLeft: `${level * 20 + 12}px` }}
                    onClick={() => onPropertySelect?.(property)}
                >
                    {hasChildren && (
                        <span
                            className={`tree-expand-icon ${isExpanded ? 'expanded' : ''}`}
                            onClick={(e) => {
                                e.stopPropagation();
                                toggleExpanded(property.id);
                            }}
                        >
                            {isExpanded ? '▼' : '▶'}
                        </span>
                    )}
                    {!hasChildren && <span className="tree-expand-spacer"></span>}
                    <div className="tree-node-info">
                        <div className="tree-node-main">
                            <span className="tree-node-name">{property.name}</span>
                            <span className="tree-node-type">({property.type})</span>
                        </div>
                        {property.metadata && (
                            <div className="tree-node-metadata">
                                {property.metadata.propertyUse ? (
                                    <span className="metadata-item">Use: {String(property.metadata.propertyUse)}</span>
                                ) : null}
                                {property.metadata.grossLeasableArea ? (
                                    <span className="metadata-item">GLA: {String(property.metadata.grossLeasableArea)}</span>
                                ) : null}
                            </div>
                        )}
                    </div>
                </div>
                {hasChildren && isExpanded && (
                    <div className="tree-children">
                        {property.children!.map(child => renderTreeNode(child, level + 1))}
                    </div>
                )}
            </div>
        );
    };

    const treeData = buildTree(properties);

    return (
        <div className="tree-view">
            <div className="tree-header">
                <h3>Property Hierarchy</h3>
            </div>
            <div className="tree-content">
                {treeData.map(property => renderTreeNode(property))}
            </div>
        </div>
    );
};

interface GoJSDiagramProps {
    width?: number;
    height?: number;
    context?: ComponentFramework.Context<unknown>;
    entityId?: string | null;
}

export const GoJSDiagramComponent: React.FC<GoJSDiagramProps> = ({
    width = 800,
    height = 600,
    context,
    entityId
}) => {
    // Ensure we have valid dimensions
    const finalWidth = width && width > 0 ? width : 800;
    const finalHeight = height && height > 0 ? height : 600;

    console.log('🚀 GoJSDiagramComponent rendering with props:', {
        originalWidth: width,
        originalHeight: height,
        finalWidth,
        finalHeight,
        hasContext: !!context,
        entityId
    });

    const diagramRef = useRef<HTMLDivElement>(null);
    const [diagram, setDiagram] = useState<go.Diagram | null>(null);
    const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
    const [properties, setProperties] = useState<Property[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [dataSource, setDataSource] = useState<'mock' | 'crm'>('mock');
    const [error, setError] = useState<string | null>(null);

    // Calculate dimensions for split layout (40% tree, 60% diagram)
    const treeWidth = Math.floor(finalWidth * 0.4);
    const diagramWidth = finalWidth - treeWidth;

    // Data loading effect
    useEffect(() => {
        const loadData = async () => {
            console.log('🔄 Loading property data...');
            setLoading(true);
            setError(null);

            try {
                const dataService = new PropertyDataService(context, entityId);
                const result: DataLoadResult = await dataService.loadPropertyData();

                setProperties(result.properties);
                setDataSource(result.source);

                if (!result.success && result.error) {
                    setError(result.error);
                    console.warn('⚠️ Data loading completed with warnings:', result.error);
                } else {
                    console.log(`✅ Data loaded successfully from ${result.source}: ${result.properties.length} properties`);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                setError(`Failed to load property data: ${errorMessage}`);
                console.error('❌ Data loading failed:', error);
                setProperties([]); // Set empty array on error
            } finally {
                setLoading(false);
            }
        };

        loadData();
    }, [context, entityId]); // Re-run when context or entityId changes

    const getRealPropertyData = (): PropertyModelData => {
        console.log('🔄 Getting property data from state...');

        console.log('📊 Properties array from state:', properties.length, 'properties');
        console.log('🔍 Properties details:', properties);

        // Convert properties to node data
        const nodeDataArray: PropertyNodeData[] = properties.map(prop => ({
            key: prop.id,
            text: `${prop.name}\n(${prop.type})`,
            property: prop,
            isMainProperty: prop.isMainProperty || false,
            color: getPropertyColor(prop)
        }));

        console.log('🔗 Created node data array:', nodeDataArray.length, 'nodes');
        console.log('📋 Node data details:', nodeDataArray);

        // Create links based on parent-child relationships
        const linkDataArray: PropertyLinkData[] = properties
            .filter(prop => prop.parentPropertyId)
            .map(prop => ({
                from: prop.parentPropertyId!,
                to: prop.id,
                relationship: "contains",
                color: "#666"
            }));

        console.log('🔗 Created link data array:', linkDataArray.length, 'links');
        console.log('📋 Link data details:', linkDataArray);

        const modelData = {
            nodeDataArray,
            linkDataArray
        };

        console.log('✅ Property model data created successfully:', modelData);
        return modelData;
    };

    // All panels will have white background with gray borders
    const getPropertyColor = (property: Property): string => {
        const color = "white"; // All panels have white background
        console.log(`🎨 Color for property "${property.name}" (${property.type}):`, color);
        return color;
    };

    // Generate random background color for image placeholder
    const getRandomColor = (seed: string): string => {
        const colors = [
            "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
            "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
            "#F8C471", "#82E0AA", "#F1948A", "#85C1E9", "#D7BDE2"
        ];
        let hash = 0;
        for (let i = 0; i < seed.length; i++) {
            hash = seed.charCodeAt(i) + ((hash << 5) - hash);
        }
        return colors[Math.abs(hash) % colors.length];
    };

    // Get initials from first two words of property name
    const getInitials = (name: string): string => {
        const words = name.trim().split(/\s+/);
        if (words.length >= 2) {
            return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
        } else if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        }
        return "P"; // Default fallback
    };

    // Get icon based on property type (supports both names and numeric values)
    const getPropertyIcon = (propertyType: string): string => {
        // Handle numeric property type values
        const numericType = parseInt(propertyType);
        if (!isNaN(numericType)) {
            switch (numericType) {
                case 100000000: return "🏞️";    // Park
                case 100000001: return "🌾";    // Landplot
                case 100000002: return "🏢";    // Building
                case 100000003: return "📐";    // Section
                case 100000004: return "🏬";    // Floor
                case 100000005: return "🏠";    // Unit
                default: return "📍";          // Default location icon
            }
        }

        // Handle string property type names (fallback)
        switch (propertyType.toLowerCase()) {
            case "park": return "🏞️";        // Park icon
            case "landplot": return "🌾";    // Land plot icon
            case "building": return "🏢";     // Building icon
            case "section": return "📐";      // Section icon
            case "floor": return "🏬";        // Floor icon
            case "unit": return "🏠";         // Unit icon
            default: return "📍";            // Default location icon
        }
    };

    // Initialize the diagram
    useEffect(() => {
        console.log('🚀 Initializing GoJS diagram...');

        if (!diagramRef.current) {
            console.error('❌ Diagram ref is not available');
            return;
        }

        if (loading) {
            console.log('⏳ Waiting for data to load before initializing diagram...');
            return;
        }

        console.log('✅ Diagram ref found and data loaded, creating diagram...');
        const $ = go.GraphObject.make;

        // Create the diagram
        console.log('📊 Creating new GoJS diagram instance...');
        const newDiagram = $(go.Diagram, diagramRef.current, {
            "undoManager.isEnabled": true,
            layout: $(go.TreeLayout, {
                angle: 90,
                layerSpacing: 40,
                nodeSpacing: 20,
                arrangement: go.TreeLayout.ArrangementHorizontal
            }),
            allowCopy: false,
            allowDelete: false,
            allowMove: true,
            hasHorizontalScrollbar: true,
            hasVerticalScrollbar: true
        });

        console.log('✅ Diagram created successfully');

        // Define the node template for properties
        console.log('🎨 Setting up node template...');
        newDiagram.nodeTemplate = $(go.Node, "Position",
            {
                selectionAdorned: true,
                resizable: false,
                layoutConditions: go.Part.LayoutStandard & ~go.Part.LayoutNodeSized
            },
            // Main panel background
            $(go.Shape, "RoundedRectangle",
                {
                    strokeWidth: 1,
                    stroke: "#ccc",
                    fill: "white",
                    portId: "",
                    cursor: "pointer",
                    fromLinkable: true,
                    toLinkable: true,
                    width: 250,
                    height: NaN,
                    parameter1: 8, // Corner radius for rounded borders
                    position: new go.Point(0, 16) // Offset down to make room for circle
                },
                new go.Binding("desiredSize", "property", (prop: Property) => {
                    // Calculate height based on content
                    let lines = 2; // Name + Type
                    if (prop.metadata?.grossLeasableArea) lines++;
                    if (prop.metadata?.propertyUse) lines++;
                    const contentHeight = (lines * 12) + 30; // 12px per line + reduced margins
                    return new go.Size(250, contentHeight);
                })
            ),
            // Content panel
            $(go.Panel, "Vertical",
                {
                    margin: new go.Margin(18, 10, 6, 10), // Further reduced margins: top 18, sides 10, bottom 6
                    alignment: go.Spot.Left,
                    defaultAlignment: go.Spot.Left,
                    position: new go.Point(0, 16) // Match the background offset
                },
                $(go.TextBlock,
                    {
                        font: "bold 14px sans-serif",
                        stroke: '#333',
                        maxSize: new go.Size(226, NaN), // 250 - 24 (margin)
                        wrap: go.TextBlock.WrapFit,
                        textAlign: "left",
                        alignment: go.Spot.Left
                    },
                    new go.Binding("text", "property", (prop: Property) => prop.name)
                ),
                $(go.TextBlock,
                    {
                        font: "10px sans-serif",
                        stroke: '#666',
                        maxSize: new go.Size(226, NaN),
                        wrap: go.TextBlock.WrapFit,
                        textAlign: "left",
                        alignment: go.Spot.Left,
                        margin: new go.Margin(2, 0, 0, 0)
                    },
                    new go.Binding("text", "property", (prop: Property) => `Type: ${prop.type}`)
                ),
                $(go.TextBlock,
                    {
                        font: "10px sans-serif",
                        stroke: '#666',
                        maxSize: new go.Size(226, NaN),
                        wrap: go.TextBlock.WrapFit,
                        textAlign: "left",
                        alignment: go.Spot.Left,
                        margin: new go.Margin(2, 0, 0, 0),
                        visible: false
                    },
                    new go.Binding("text", "property", (prop: Property) => {
                        const gla = prop.metadata?.grossLeasableArea;
                        return gla ? `GLA: ${gla}` : "";
                    }),
                    new go.Binding("visible", "property", (prop: Property) => !!prop.metadata?.grossLeasableArea)
                ),
                $(go.TextBlock,
                    {
                        font: "10px sans-serif",
                        stroke: '#666',
                        maxSize: new go.Size(226, NaN),
                        wrap: go.TextBlock.WrapFit,
                        textAlign: "left",
                        alignment: go.Spot.Left,
                        margin: new go.Margin(2, 0, 0, 0),
                        visible: false
                    },
                    new go.Binding("text", "property", (prop: Property) => {
                        const use = prop.metadata?.propertyUse;
                        return use ? `Use: ${use}` : "";
                    }),
                    new go.Binding("visible", "property", (prop: Property) => !!prop.metadata?.propertyUse)
                )
            ),
            // Overlapping circle positioned over the border
            $(go.Panel, "Auto",
                {
                    position: new go.Point(12.5, 0) // 5% indent from left, at top border
                },
                $(go.Shape, "Circle",
                    {
                        width: 32,
                        height: 32,
                        strokeWidth: 2,
                        stroke: "white" // White border to create separation from background
                    },
                    new go.Binding("fill", "property", (prop: Property) => getRandomColor(prop.name))
                ),
                $(go.TextBlock,
                    {
                        font: "bold 12px sans-serif",
                        stroke: "white",
                        textAlign: "center"
                    },
                    new go.Binding("text", "property", (prop: Property) => getInitials(prop.name))
                )
            ),
            // Property type icon in top right corner
            $(go.TextBlock,
                {
                    font: "16px sans-serif",
                    textAlign: "center",
                    position: new go.Point(220, 8) // Top right corner (250px - 30px margin)
                },
                new go.Binding("text", "property", (prop: Property) => getPropertyIcon(prop.type))
            )
        );

        console.log('✅ Node template configured');

        // Define the link template for property relationships
        console.log('🔗 Setting up link template...');
        newDiagram.linkTemplate = $(go.Link,
            {
                routing: go.Link.Orthogonal,
                corner: 5,
                selectable: false
            },
            $(go.Shape,
                {
                    strokeWidth: 1,
                    stroke: "#999"
                }
            ),
            $(go.Shape,
                {
                    toArrow: "Standard",
                    stroke: "#999",
                    fill: "#999",
                    scale: 0.8
                }
            )
        );

        console.log('✅ Link template configured');

        // Load the property data into diagram
        console.log('📊 Loading property data into diagram...');
        const propertyData = getRealPropertyData();

        console.log('🔄 Creating GraphLinksModel...');
        newDiagram.model = new go.GraphLinksModel(
            propertyData.nodeDataArray,
            propertyData.linkDataArray
        );

        console.log('✅ Model loaded successfully');
        console.log('📊 Final diagram state:', newDiagram);

        setDiagram(newDiagram);
        console.log('✅ Diagram component initialization complete!');

        return () => {
            newDiagram.div = null;
        };
    }, [loading, properties]); // Re-initialize when loading state or properties change

    // Add click handler for property nodes
    useEffect(() => {
        if (!diagram) {
            console.log('⏳ Waiting for diagram to be ready for click handlers...');
            return;
        }

        console.log('🖱️ Setting up click handlers...');
        diagram.addDiagramListener("ObjectSingleClicked", (e) => {
            const part = e.subject.part;
            if (part instanceof go.Node) {
                const nodeData = part.data as PropertyNodeData;
                console.log("🖱️ Property clicked:", nodeData.property);
                console.log("📋 Full node data:", nodeData);
                setSelectedProperty(nodeData.property);
            }
        });

        console.log('✅ Click handlers configured');
    }, [diagram]);

    console.log('🎨 Rendering diagram container with dimensions:', {
        width: `${finalWidth}px`,
        height: `${finalHeight}px`,
        treeWidth: `${treeWidth}px`,
        diagramWidth: `${diagramWidth}px`
    });

    const handlePropertySelect = (property: Property) => {
        setSelectedProperty(property);
        // Optionally highlight the node in the diagram
        if (diagram) {
            const node = diagram.findNodeForKey(property.id);
            if (node) {
                diagram.select(node);
                diagram.centerRect(node.actualBounds);
            }
        }
    };

    // Show loading state
    if (loading) {
        return (
            <div className="diagram-with-tree" style={{
                width: `${finalWidth}px`,
                height: `${finalHeight}px`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '1px solid #ccc',
                backgroundColor: 'white'
            }}>
                <div style={{ textAlign: 'center', color: '#666' }}>
                    <div>🔄 Loading property data...</div>
                    <div style={{ fontSize: '12px', marginTop: '8px' }}>
                        Data source: {dataSource}
                    </div>
                </div>
            </div>
        );
    }

    // Show error state
    if (error && properties.length === 0) {
        return (
            <div className="diagram-with-tree" style={{
                width: `${finalWidth}px`,
                height: `${finalHeight}px`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: '1px solid #ccc',
                backgroundColor: 'white'
            }}>
                <div style={{ textAlign: 'center', color: '#d32f2f' }}>
                    <div>❌ Error loading property data</div>
                    <div style={{ fontSize: '12px', marginTop: '8px', maxWidth: '400px' }}>
                        {error}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="diagram-with-tree" style={{
            width: `${finalWidth}px`,
            height: `${finalHeight}px`,
            display: 'flex',
            border: '1px solid #ccc',
            backgroundColor: 'white',
            overflow: 'hidden'
        }}>
            <div className="tree-panel" style={{
                width: `${treeWidth}px`,
                height: `${finalHeight}px`,
                overflow: 'auto'
            }}>
                <TreeView
                    properties={properties}
                    selectedPropertyId={selectedProperty?.id}
                    onPropertySelect={handlePropertySelect}
                />
                {/* Data source indicator */}
                <div style={{
                    position: 'absolute',
                    bottom: '8px',
                    left: '8px',
                    fontSize: '10px',
                    color: '#666',
                    backgroundColor: 'rgba(255,255,255,0.8)',
                    padding: '2px 6px',
                    borderRadius: '3px'
                }}>
                    📊 {dataSource === 'crm' ? 'CRM Data' : 'Mock Data'}
                    {error && <span style={{ color: '#ff9800' }}> ⚠️</span>}
                </div>
            </div>
            <div
                ref={diagramRef}
                className="diagram-panel"
                style={{
                    width: `${diagramWidth}px`,
                    height: `${finalHeight}px`,
                    borderLeft: '1px solid #ccc',
                    overflow: 'hidden'
                }}
            />
        </div>
    );
};
